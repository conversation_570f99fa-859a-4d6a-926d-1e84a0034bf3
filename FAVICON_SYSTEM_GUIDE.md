# Dynamic Favicon System Guide

This guide explains how the theme-based favicon system works in your WeForward ecosystem.

## Overview

The favicon system automatically changes the browser tab icon based on which company section the user is visiting, providing a seamless brand experience across your multi-theme website.

## How It Works

### 1. Automatic Theme Detection
Each company section has its own layout file that defines theme-specific metadata:

- **Homepage** (`/`) - Uses default WeForward favicon
- **Forest Forward** (`/forestforward/*`) - Uses Forest Forward icon
- **Lagom Events** (`/lagom/*`) - Uses Lagom icon  
- **Story Forward** (`/storyforward/*`) - Uses Story Forward icon

### 2. Next.js Metadata API
The system leverages Next.js 13+ App Router metadata API for optimal performance:

```javascript
// In each layout.js file
export const metadata = {
  title: "Company Name - Tagline",
  description: "Company description",
  icons: {
    icon: '/brand/company/icon.jpg',
  },
};
```

### 3. Existing Brand Assets
Uses your existing brand icons from `/public/brand/` folders:
- `/brand/forestforward/icon.jpg`
- `/brand/lagom/icon.jpg`
- `/brand/storyforward/icon.jpg`

## File Structure

```
src/app/
├── utils/
│   └── favicon.js              # Favicon utility functions
├── layout.js                   # Root layout (default favicon)
├── forestforward/
│   └── layout.js              # Forest Forward favicon
├── lagom/
│   └── layout.js              # Lagom favicon
└── storyforward/
    └── layout.js              # Story Forward favicon

public/
├── favicon.ico                 # Default favicon
└── brand/
    ├── forestforward/
    │   └── icon.jpg           # Forest Forward favicon
    ├── lagom/
    │   └── icon.jpg           # Lagom favicon
    └── storyforward/
        └── icon.jpg           # Story Forward favicon
```

## Current Implementation

### Root Layout (Default)
```javascript
// src/app/layout.js
export const metadata = {
  title: "WeForward Ecosystem",
  description: "Walk the talk - samen maken we duurzaamheid tastbaar",
  icons: {
    icon: '/favicon.ico',
  },
};
```

### Company Layouts
Each company section automatically gets its themed favicon:

```javascript
// src/app/forestforward/layout.js
export const metadata = {
  title: "Forest Forward - Duurzame Bosbouw",
  description: "Creëer impact met bedrijfsbossen, schoolbossen en natuuropwaardering",
  icons: {
    icon: '/brand/forestforward/icon.jpg',
  },
};
```

## Benefits

1. **Zero Runtime Overhead** - Uses Next.js static metadata, no JavaScript needed
2. **SEO Optimized** - Proper meta titles and descriptions per section
3. **Brand Consistency** - Each section shows its appropriate brand icon
4. **Easy Maintenance** - Centralized configuration in utility file
5. **Automatic** - Works seamlessly with your existing theme system

## Adding New Company Sections

To add a favicon for a new company section:

1. **Add brand icon** to `/public/brand/newcompany/icon.jpg`

2. **Update favicon utility**:
```javascript
// src/app/utils/favicon.js
export const THEME_FAVICONS = {
  // ... existing themes
  newcompany: {
    title: "New Company - Tagline",
    description: "Company description",
    icon: '/brand/newcompany/icon.jpg',
  },
};
```

3. **Create layout file**:
```javascript
// src/app/newcompany/layout.js
export const metadata = {
  title: "New Company - Tagline",
  description: "Company description", 
  icons: {
    icon: '/brand/newcompany/icon.jpg',
  },
};

export default function NewCompanyLayout({ children }) {
  return (
    <div className="theme-newcompany">
      {children}
    </div>
  );
}
```

## Browser Support

- ✅ All modern browsers (Chrome, Firefox, Safari, Edge)
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)
- ✅ Progressive Web Apps (PWA)
- ✅ Browser bookmarks

## Testing

To test the favicon system:

1. **Navigate between sections** and watch the browser tab icon change
2. **Bookmark pages** from different sections to see themed icons
3. **Check mobile** - add to home screen to see app icons
4. **Verify SEO** - check page titles in search results

## Troubleshooting

### Favicon Not Updating
- **Clear browser cache** (Ctrl+F5 or Cmd+Shift+R)
- **Check file paths** - ensure icon files exist in `/public/brand/`
- **Verify metadata** - check layout.js files have correct metadata export

### Icon Quality Issues
- **Use square images** (recommended: 512x512px minimum)
- **Optimize file size** - keep under 100KB for fast loading
- **Test different formats** - .ico, .png, .jpg all supported

## Future Enhancements

Possible improvements for the future:

1. **Multiple icon sizes** - Add 16x16, 32x32, 192x192 variants
2. **PWA manifest** - Include icons in web app manifest
3. **Dark mode icons** - Theme-aware icons for dark/light mode
4. **Animated favicons** - Subtle animations for enhanced branding
