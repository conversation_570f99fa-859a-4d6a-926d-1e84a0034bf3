/**
 * Favicon Utility for Theme-Based Icons
 * 
 * This utility provides theme-specific favicon configurations that work
 * seamlessly with your existing theme system.
 * 
 * Usage in layout files:
 * ```javascript
 * import { getFaviconMetadata } from '../utils/favicon';
 * 
 * export const metadata = getFaviconMetadata('forestforward');
 * ```
 */

export const THEME_FAVICONS = {
  homepage: {
    title: "WeForward Ecosystem",
    description: "Walk the talk - samen maken we duurzaamheid tastbaar",
    icon: '/favicon.ico',
  },
  forestforward: {
    title: "Forest Forward - Duurzame Bosbouw",
    description: "Creëer impact met bed<PERSON><PERSON><PERSON><PERSON><PERSON>, schoolbossen en natuuropwaardering",
    icon: '/brand/forestforward/icon.jpg',
  },
  lagom: {
    title: "Lagom Events - Verbindende Evenementen", 
    description: "Creëer betrokkenheid en verbinding door impactvolle evenementen",
    icon: '/brand/lagom/icon.jpg',
  },
  storyforward: {
    title: "Story Forward - Strategische Communicatie",
    description: "<PERSON><PERSON> jouw bedrijf een stem en inspireer anderen met krachtige verhalen",
    icon: '/brand/storyforward/icon.jpg',
  },
};

/**
 * Get favicon metadata for a specific theme
 * @param {string} theme - The theme name (homepage, forestforward, lagom, storyforward)
 * @returns {object} Next.js metadata object with favicon configuration
 */
export function getFaviconMetadata(theme = 'homepage') {
  const config = THEME_FAVICONS[theme] || THEME_FAVICONS.homepage;
  
  return {
    title: config.title,
    description: config.description,
    icons: {
      icon: config.icon,
    },
  };
}

/**
 * Get just the favicon icon path for a theme
 * @param {string} theme - The theme name
 * @returns {string} Path to the favicon icon
 */
export function getFaviconIcon(theme = 'homepage') {
  const config = THEME_FAVICONS[theme] || THEME_FAVICONS.homepage;
  return config.icon;
}
